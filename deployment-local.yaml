apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-dashboard
  namespace: default
  labels:
    app: ryvyl-dashboard
    environment: local
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ryvyl-dashboard
      environment: local
  template:
    metadata:
      labels:
        app: ryvyl-dashboard
        environment: local
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ryvyl-dashboard:local
        imagePullPolicy: Never
        ports:
        - containerPort: 3001
          name: http
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
