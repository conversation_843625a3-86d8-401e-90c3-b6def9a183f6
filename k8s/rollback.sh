#!/bin/bash

# Kubernetes Rollback Script for RYVYL Dashboard
# This script provides quick rollback capabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="staging"
NAMESPACE=""
REVISION=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Rollback RYVYL Dashboard deployment in Kubernetes

OPTIONS:
    -e, --environment    Environment (staging|production) [default: staging]
    -n, --namespace     Kubernetes namespace [auto-detected from environment]
    -r, --revision      Specific revision to rollback to [optional]
    -h, --help          Show this help message

EXAMPLES:
    $0 -e staging
    $0 -e production -r 3
    $0 --environment staging --revision 2

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--revision)
            REVISION="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Environment must be 'staging' or 'production'"
    exit 1
fi

# Set namespace if not provided
if [[ -z "$NAMESPACE" ]]; then
    NAMESPACE="ryvyl-${ENVIRONMENT}"
fi

print_status "Starting rollback for $ENVIRONMENT environment"
print_status "Namespace: $NAMESPACE"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

# Show rollout history
print_status "Deployment rollout history:"
kubectl rollout history deployment/ryvyl-dashboard -n "$NAMESPACE"

# Perform rollback
if [[ -n "$REVISION" ]]; then
    print_status "Rolling back to revision $REVISION"
    kubectl rollout undo deployment/ryvyl-dashboard --to-revision="$REVISION" -n "$NAMESPACE"
else
    print_status "Rolling back to previous revision"
    kubectl rollout undo deployment/ryvyl-dashboard -n "$NAMESPACE"
fi

# Wait for rollback to complete
print_status "Waiting for rollback to complete"
kubectl rollout status deployment/ryvyl-dashboard -n "$NAMESPACE" --timeout=300s

if [[ $? -eq 0 ]]; then
    print_success "Rollback completed successfully"
    
    # Show current status
    print_status "Current deployment status:"
    kubectl get pods -n "$NAMESPACE" -l app=ryvyl-dashboard
else
    print_error "Rollback failed or timed out"
    exit 1
fi

print_success "Rollback operation completed!"
