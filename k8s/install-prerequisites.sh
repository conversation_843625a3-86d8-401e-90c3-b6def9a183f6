#!/bin/bash

# Prerequisites Installation Script for RYVYL Dashboard Kubernetes Testing
# This script installs Docker, kubectl, and other required tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================================="
    echo "$1"
    echo "=================================================="
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_status "Detected macOS"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_status "Detected Linux"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Install Homebrew on macOS
install_homebrew() {
    if command -v brew &> /dev/null; then
        print_success "Homebrew already installed"
        return 0
    fi
    
    print_status "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ -f "/opt/homebrew/bin/brew" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    print_success "Homebrew installed successfully"
}

# Install Docker
install_docker() {
    print_header "Installing Docker"
    
    if command -v docker &> /dev/null; then
        print_success "Docker already installed: $(docker --version)"
        return 0
    fi
    
    case $OS in
        macos)
            install_homebrew
            print_status "Installing Docker Desktop via Homebrew..."
            brew install --cask docker
            print_success "Docker Desktop installed"
            print_warning "Please start Docker Desktop from Applications and enable Kubernetes"
            print_warning "Then run this script again or continue manually"
            ;;
        linux)
            print_status "Installing Docker on Linux..."
            # Update package index
            sudo apt-get update
            
            # Install prerequisites
            sudo apt-get install -y \
                ca-certificates \
                curl \
                gnupg \
                lsb-release
            
            # Add Docker's official GPG key
            sudo mkdir -p /etc/apt/keyrings
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
            
            # Set up repository
            echo \
                "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
                $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # Install Docker Engine
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            
            # Add user to docker group
            sudo usermod -aG docker $USER
            
            print_success "Docker installed successfully"
            print_warning "Please log out and back in for group changes to take effect"
            ;;
    esac
}

# Install kubectl
install_kubectl() {
    print_header "Installing kubectl"
    
    if command -v kubectl &> /dev/null; then
        print_success "kubectl already installed: $(kubectl version --client --short 2>/dev/null || echo 'Unknown version')"
        return 0
    fi
    
    case $OS in
        macos)
            if command -v brew &> /dev/null; then
                print_status "Installing kubectl via Homebrew..."
                brew install kubectl
            else
                print_status "Installing kubectl manually..."
                curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/darwin/amd64/kubectl"
                chmod +x kubectl
                sudo mv kubectl /usr/local/bin/
            fi
            ;;
        linux)
            print_status "Installing kubectl on Linux..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            ;;
    esac
    
    print_success "kubectl installed successfully"
}

# Install Helm (optional but recommended)
install_helm() {
    print_header "Installing Helm (optional)"
    
    if command -v helm &> /dev/null; then
        print_success "Helm already installed: $(helm version --short 2>/dev/null || echo 'Unknown version')"
        return 0
    fi
    
    read -p "Would you like to install Helm? (recommended for advanced Kubernetes management) (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Skipping Helm installation"
        return 0
    fi
    
    case $OS in
        macos)
            if command -v brew &> /dev/null; then
                print_status "Installing Helm via Homebrew..."
                brew install helm
            else
                print_status "Installing Helm manually..."
                curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
            fi
            ;;
        linux)
            print_status "Installing Helm on Linux..."
            curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
            ;;
    esac
    
    print_success "Helm installed successfully"
}

# Install Minikube (alternative to Docker Desktop Kubernetes)
install_minikube() {
    print_header "Installing Minikube (optional)"
    
    if command -v minikube &> /dev/null; then
        print_success "Minikube already installed: $(minikube version --short 2>/dev/null || echo 'Unknown version')"
        return 0
    fi
    
    read -p "Would you like to install Minikube? (alternative to Docker Desktop Kubernetes) (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Skipping Minikube installation"
        return 0
    fi
    
    case $OS in
        macos)
            if command -v brew &> /dev/null; then
                print_status "Installing Minikube via Homebrew..."
                brew install minikube
            else
                print_status "Installing Minikube manually..."
                curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-darwin-amd64
                sudo install minikube-darwin-amd64 /usr/local/bin/minikube
                rm minikube-darwin-amd64
            fi
            ;;
        linux)
            print_status "Installing Minikube on Linux..."
            curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
            sudo install minikube-linux-amd64 /usr/local/bin/minikube
            rm minikube-linux-amd64
            ;;
    esac
    
    print_success "Minikube installed successfully"
}

# Setup shell environment
setup_environment() {
    print_header "Setting up shell environment"
    
    # Determine shell config file
    if [[ "$SHELL" == *"zsh"* ]]; then
        SHELL_CONFIG="$HOME/.zshrc"
    elif [[ "$SHELL" == *"bash"* ]]; then
        SHELL_CONFIG="$HOME/.bash_profile"
    else
        SHELL_CONFIG="$HOME/.profile"
    fi
    
    print_status "Using shell config: $SHELL_CONFIG"
    
    # Add Docker to PATH if on macOS
    if [[ "$OS" == "macos" ]] && [[ -f "/Applications/Docker.app/Contents/Resources/bin/docker" ]]; then
        if ! grep -q "Docker.app/Contents/Resources/bin" "$SHELL_CONFIG" 2>/dev/null; then
            echo 'export PATH="/Applications/Docker.app/Contents/Resources/bin:$PATH"' >> "$SHELL_CONFIG"
            print_status "Added Docker to PATH in $SHELL_CONFIG"
        fi
    fi
    
    # Add Homebrew to PATH for Apple Silicon
    if [[ "$OS" == "macos" ]] && [[ -f "/opt/homebrew/bin/brew" ]]; then
        if ! grep -q "/opt/homebrew/bin/brew shellenv" "$SHELL_CONFIG" 2>/dev/null; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> "$SHELL_CONFIG"
            print_status "Added Homebrew to PATH in $SHELL_CONFIG"
        fi
    fi
    
    print_success "Shell environment configured"
    print_warning "Please restart your terminal or run: source $SHELL_CONFIG"
}

# Verify installation
verify_installation() {
    print_header "Verifying Installation"
    
    local all_good=true
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_success "✅ Docker: $(docker --version 2>/dev/null || echo 'Installed but not running')"
    else
        print_error "❌ Docker not found"
        all_good=false
    fi
    
    # Check kubectl
    if command -v kubectl &> /dev/null; then
        print_success "✅ kubectl: $(kubectl version --client --short 2>/dev/null || echo 'Installed')"
    else
        print_error "❌ kubectl not found"
        all_good=false
    fi
    
    # Check Helm (optional)
    if command -v helm &> /dev/null; then
        print_success "✅ Helm: $(helm version --short 2>/dev/null || echo 'Installed')"
    else
        print_status "ℹ️  Helm: Not installed (optional)"
    fi
    
    # Check Minikube (optional)
    if command -v minikube &> /dev/null; then
        print_success "✅ Minikube: $(minikube version --short 2>/dev/null || echo 'Installed')"
    else
        print_status "ℹ️  Minikube: Not installed (optional)"
    fi
    
    if $all_good; then
        print_success "🎉 All required tools are installed!"
        echo ""
        echo "Next steps:"
        echo "1. Start Docker Desktop (if on macOS)"
        echo "2. Enable Kubernetes in Docker Desktop settings"
        echo "3. Run: ./check-setup.sh to verify everything works"
        echo "4. Run: ./test-simple.sh deploy to test your application"
    else
        print_error "Some tools are missing. Please check the errors above."
    fi
}

# Main installation process
main() {
    echo "🚀 RYVYL Dashboard - Prerequisites Installation"
    echo "This script will install Docker, kubectl, and other required tools"
    echo ""
    
    detect_os
    
    # Ask for confirmation
    read -p "Continue with installation? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Installation cancelled"
        exit 0
    fi
    
    install_docker
    install_kubectl
    install_helm
    install_minikube
    setup_environment
    verify_installation
    
    print_header "Installation Complete!"
    print_status "Please restart your terminal and run './check-setup.sh' to verify everything works"
}

# Show usage if help requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "Usage: $0"
    echo ""
    echo "This script installs prerequisites for RYVYL Dashboard Kubernetes testing:"
    echo "- Docker Desktop (macOS) or Docker Engine (Linux)"
    echo "- kubectl"
    echo "- Helm (optional)"
    echo "- Minikube (optional)"
    echo ""
    echo "The script will prompt before installing optional components."
    exit 0
fi

main "$@"
