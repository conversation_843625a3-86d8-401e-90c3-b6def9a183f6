#!/bin/bash

# Debug script to help troubleshoot Docker build issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================================="
    echo "$1"
    echo "=================================================="
}

print_header "Docker Build Debug Information"

# Check current directory
print_status "Current working directory: $(pwd)"

# Check if we're in k8s directory
if [[ "$(basename $(pwd))" == "k8s" ]]; then
    print_success "Currently in k8s directory"
    print_status "Changing to parent directory..."
    cd ..
else
    print_status "Not in k8s directory"
fi

print_status "Project root directory: $(pwd)"

# List all files in current directory
print_header "Files in Project Root"
ls -la

# Check for Dockerfiles
print_header "Dockerfile Check"
if [[ -f "Dockerfile" ]]; then
    print_success "✅ Dockerfile found"
    print_status "Size: $(ls -lh Dockerfile | awk '{print $5}')"
else
    print_error "❌ Dockerfile not found"
fi

if [[ -f "Dockerfile.production" ]]; then
    print_success "✅ Dockerfile.production found"
    print_status "Size: $(ls -lh Dockerfile.production | awk '{print $5}')"
    print_status "First few lines:"
    head -5 Dockerfile.production
else
    print_error "❌ Dockerfile.production not found"
fi

# Check Docker
print_header "Docker Environment"
if command -v docker &> /dev/null; then
    print_success "✅ Docker command available"
    print_status "Docker version: $(docker --version)"
    
    if docker info &> /dev/null; then
        print_success "✅ Docker daemon is running"
        print_status "Docker info:"
        docker info --format '{{.ServerVersion}}' 2>/dev/null || echo "Unknown version"
    else
        print_error "❌ Docker daemon is not running"
        print_status "Please start Docker Desktop"
    fi
else
    print_error "❌ Docker command not found"
fi

# Test Docker build
print_header "Docker Build Test"

if [[ -f "Dockerfile.production" ]] && command -v docker &> /dev/null && docker info &> /dev/null; then
    print_status "Testing Docker build..."
    
    # Try building with verbose output
    print_status "Running: docker build -f Dockerfile.production -t ryvyl-dashboard:debug-test ."
    
    if docker build -f Dockerfile.production -t ryvyl-dashboard:debug-test . --no-cache --progress=plain; then
        print_success "✅ Docker build successful!"
        
        # Check if image was created
        if docker images ryvyl-dashboard:debug-test --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep debug-test; then
            print_success "✅ Image created successfully"
            
            # Clean up test image
            docker rmi ryvyl-dashboard:debug-test &> /dev/null || true
            print_status "Cleaned up test image"
        fi
    else
        print_error "❌ Docker build failed"
        print_status "Check the error messages above for details"
    fi
else
    print_error "Cannot test Docker build - missing requirements"
fi

# Check package.json
print_header "Project Files Check"
if [[ -f "package.json" ]]; then
    print_success "✅ package.json found"
    print_status "Project name: $(grep '"name"' package.json | cut -d'"' -f4)"
else
    print_error "❌ package.json not found"
fi

if [[ -f "nginx.conf" ]]; then
    print_success "✅ nginx.conf found"
else
    print_error "❌ nginx.conf not found"
fi

# Recommendations
print_header "Recommendations"

echo "If Docker build is failing:"
echo "1. Make sure Docker Desktop is running"
echo "2. Try building manually:"
echo "   cd $(pwd)"
echo "   docker build -f Dockerfile.production -t ryvyl-dashboard:test ."
echo ""
echo "3. If that works, try the test script:"
echo "   cd k8s"
echo "   ./test-simple.sh deploy"
echo ""
echo "4. Check Docker Desktop has enough resources:"
echo "   - Memory: 8GB recommended"
echo "   - Disk space: 10GB+ available"

print_header "Debug Complete"
