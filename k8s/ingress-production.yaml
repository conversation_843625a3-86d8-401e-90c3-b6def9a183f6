apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-dashboard-ingress
  namespace: ryvyl-production
  labels:
    app: ryvyl-dashboard
    environment: production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
spec:
  tls:
  - hosts:
    - ryvyl-dashboard.yourdomain.com
    secretName: ryvyl-dashboard-production-tls
  rules:
  - host: ryvyl-dashboard.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-dashboard-service
            port:
              number: 80
