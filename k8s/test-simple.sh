#!/bin/bash

# Simple Local Testing Script for RYVYL Dashboard
# This script provides basic testing without ingress controller

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
Usage: $0 [COMMAND]

Simple local testing for RYVYL Dashboard (no ingress required)

COMMANDS:
    deploy      Deploy application locally
    test        Test the deployment
    update      Test rolling update
    cleanup     Clean up resources
    status      Show current status

EXAMPLES:
    $0 deploy
    $0 test
    $0 update
    $0 cleanup

EOF
}

# Check prerequisites
check_prerequisites() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed"
        print_status "Install kubectl: https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi

    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        print_status "Make sure Docker Desktop Kubernetes is enabled or you have a local cluster running"
        print_status "In Docker Desktop: Settings → Kubernetes → Enable Kubernetes"
        exit 1
    fi

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        print_status "Please ensure Docker Desktop is installed and running"
        print_status "Download from: https://www.docker.com/products/docker-desktop"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        print_status "Please start Docker Desktop and try again"
        exit 1
    fi
}

# Build and deploy
deploy_app() {
    print_status "Building and deploying RYVYL Dashboard locally..."
    
    # Build the image
    print_status "Building Docker image..."

    # Change to project root
    cd ..

    # Verify Dockerfile exists
    if [[ ! -f "Dockerfile.production" ]]; then
        print_error "Dockerfile.production not found in project root"
        print_status "Current directory: $(pwd)"
        print_status "Files: $(ls -la | grep Dockerfile || echo 'No Dockerfile found')"
        exit 1
    fi

    # Build the Docker image
    print_status "Building from $(pwd) with Dockerfile.production"
    if ! docker build -f Dockerfile.production -t ryvyl-dashboard:local .; then
        print_warning "Dockerfile.production failed, trying Dockerfile.simple..."
        if ! docker build -f Dockerfile.simple -t ryvyl-dashboard:local .; then
            print_error "Both Dockerfile.production and Dockerfile.simple failed"
            print_status "Try building manually: docker build -t ryvyl-dashboard:local ."
            exit 1
        fi
        print_success "Built successfully with Dockerfile.simple"
    else
        print_success "Built successfully with Dockerfile.production"
    fi

    # Return to k8s directory
    cd k8s
    
    # Create deployment
    print_status "Creating Kubernetes deployment..."
    cat > deployment-simple.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-dashboard
  labels:
    app: ryvyl-dashboard
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ryvyl-dashboard
  template:
    metadata:
      labels:
        app: ryvyl-dashboard
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ryvyl-dashboard:local
        imagePullPolicy: Never
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
EOF

    # Create service
    cat > service-simple.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-dashboard-service
  labels:
    app: ryvyl-dashboard
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: ryvyl-dashboard
EOF

    # Apply configurations
    kubectl apply -f deployment-simple.yaml
    kubectl apply -f service-simple.yaml
    
    # Wait for deployment
    print_status "Waiting for deployment to be ready..."
    kubectl rollout status deployment/ryvyl-dashboard --timeout=300s
    
    print_success "Application deployed successfully!"
    print_status "Use 'kubectl port-forward svc/ryvyl-dashboard-service 8080:80' to access the app"
}

# Test the deployment
test_deployment() {
    print_status "Testing the deployment..."
    
    # Check if deployment exists
    if ! kubectl get deployment ryvyl-dashboard &> /dev/null; then
        print_error "Deployment not found. Run '$0 deploy' first."
        exit 1
    fi
    
    # Check pod status
    print_status "Checking pod status..."
    kubectl get pods -l app=ryvyl-dashboard
    
    # Start port forwarding in background
    print_status "Starting port forwarding..."
    kubectl port-forward svc/ryvyl-dashboard-service 8080:80 &
    PORT_FORWARD_PID=$!
    
    # Wait a moment for port forwarding to establish
    sleep 3
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    if curl -f http://localhost:8080/health &> /dev/null; then
        print_success "✅ Health check passed"
    else
        print_error "❌ Health check failed"
    fi
    
    # Test main application
    print_status "Testing main application..."
    if curl -f http://localhost:8080 &> /dev/null; then
        print_success "✅ Application is responding"
    else
        print_error "❌ Application is not responding"
    fi
    
    # Stop port forwarding
    kill $PORT_FORWARD_PID 2>/dev/null || true
    
    print_success "Testing completed!"
    print_status "To access the app manually: kubectl port-forward svc/ryvyl-dashboard-service 8080:80"
    print_status "Then open: http://localhost:8080"
}

# Test rolling update
test_update() {
    print_status "Testing zero-downtime rolling update..."
    
    if ! kubectl get deployment ryvyl-dashboard &> /dev/null; then
        print_error "Deployment not found. Run '$0 deploy' first."
        exit 1
    fi
    
    # Start port forwarding
    print_status "Starting port forwarding for monitoring..."
    kubectl port-forward svc/ryvyl-dashboard-service 8080:80 &
    PORT_FORWARD_PID=$!
    sleep 3
    
    # Start monitoring requests
    print_status "Starting request monitoring..."
    (
        while true; do
            if curl -s http://localhost:8080/health > /dev/null 2>&1; then
                echo "✅ $(date '+%H:%M:%S') - App responding"
            else
                echo "❌ $(date '+%H:%M:%S') - App not responding"
            fi
            sleep 1
        done
    ) &
    MONITOR_PID=$!
    
    # Wait a moment to see baseline
    sleep 5
    
    # Trigger rolling update
    print_status "Triggering rolling update..."
    kubectl patch deployment ryvyl-dashboard -p '{"spec":{"template":{"metadata":{"annotations":{"deployment.kubernetes.io/revision":"'$(date +%s)'"}}}}}'
    
    # Wait for rollout
    kubectl rollout status deployment/ryvyl-dashboard --timeout=300s
    
    # Let monitoring run for a bit more
    sleep 10
    
    # Stop monitoring
    kill $MONITOR_PID 2>/dev/null || true
    kill $PORT_FORWARD_PID 2>/dev/null || true
    
    print_success "Rolling update test completed!"
    print_status "Check the output above - you should see no failed requests during the update"
}

# Show status
show_status() {
    print_status "Current deployment status:"
    
    if kubectl get deployment ryvyl-dashboard &> /dev/null; then
        echo ""
        echo "Deployment:"
        kubectl get deployment ryvyl-dashboard
        
        echo ""
        echo "Pods:"
        kubectl get pods -l app=ryvyl-dashboard
        
        echo ""
        echo "Service:"
        kubectl get svc ryvyl-dashboard-service
        
        echo ""
        echo "Recent Events:"
        kubectl get events --sort-by=.metadata.creationTimestamp | tail -5
        
        echo ""
        print_status "To access the app: kubectl port-forward svc/ryvyl-dashboard-service 8080:80"
        print_status "Then open: http://localhost:8080"
    else
        print_status "No deployment found. Run '$0 deploy' to create one."
    fi
}

# Cleanup
cleanup() {
    print_status "Cleaning up resources..."
    
    kubectl delete deployment ryvyl-dashboard --ignore-not-found=true
    kubectl delete service ryvyl-dashboard-service --ignore-not-found=true
    
    # Clean up temporary files
    rm -f deployment-simple.yaml service-simple.yaml
    
    print_success "Cleanup completed!"
}

# Main script
COMMAND=${1:-""}

if [[ -z "$COMMAND" ]]; then
    show_usage
    exit 1
fi

check_prerequisites

case $COMMAND in
    deploy)
        deploy_app
        ;;
    test)
        test_deployment
        ;;
    update)
        test_update
        ;;
    status)
        show_status
        ;;
    cleanup)
        cleanup
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
