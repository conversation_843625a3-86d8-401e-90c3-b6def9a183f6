# Kubernetes Deployment for RYVYL Dashboard

This directory contains Kubernetes configurations for deploying the RYVYL Dashboard with zero-downtime capabilities.

## Overview

The Kubernetes setup provides:
- **Zero-downtime deployments** using rolling update strategy
- **Auto-scaling** based on CPU and memory usage
- **Health checks** for application reliability
- **Environment separation** (staging and production)
- **SSL termination** and ingress configuration
- **Configuration management** using ConfigMaps

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Ingress       │    │   Ingress       │
│   (Staging)     │    │   (Production)  │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Service       │    │   Service       │
│   (ClusterIP)   │    │   (ClusterIP)   │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Deployment    │    │   Deployment    │
│   (2 replicas)  │    │   (3 replicas)  │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│      HPA        │    │      HPA        │
│   (2-5 pods)    │    │   (3-10 pods)   │
└─────────────────┘    └─────────────────┘
```

## Files Structure

```
k8s/
├── README.md                    # This file
├── namespaces.yaml             # Namespace definitions
├── configmap-staging.yaml      # Staging environment config
├── configmap-production.yaml   # Production environment config
├── deployment-staging.yaml     # Staging deployment
├── deployment-production.yaml  # Production deployment
├── service-staging.yaml        # Staging service
├── service-production.yaml     # Production service
├── ingress-staging.yaml        # Staging ingress
├── ingress-production.yaml     # Production ingress
├── hpa-staging.yaml            # Staging auto-scaler
├── hpa-production.yaml         # Production auto-scaler
├── deploy.sh                   # Deployment script
└── rollback.sh                 # Rollback script
```

## Prerequisites

1. **Kubernetes cluster** (v1.19+)
2. **kubectl** configured to access your cluster
3. **NGINX Ingress Controller** installed
4. **cert-manager** for SSL certificates (optional)
5. **Metrics Server** for HPA functionality
6. **Docker registry access** to ghcr.io/encorp-io/ryvyl-dashboard

## Quick Start

### 1. Create Namespaces
```bash
kubectl apply -f namespaces.yaml
```

### 2. Create Docker Registry Secret
```bash
# For staging
kubectl create secret docker-registry ghcr-secret \
  --docker-server=ghcr.io \
  --docker-username=YOUR_USERNAME \
  --docker-password=YOUR_TOKEN \
  --namespace=ryvyl-staging

# For production
kubectl create secret docker-registry ghcr-secret \
  --docker-server=ghcr.io \
  --docker-username=YOUR_USERNAME \
  --docker-password=YOUR_TOKEN \
  --namespace=ryvyl-production
```

### 3. Update Configuration
Edit the ConfigMap files to set your API endpoints:
- `configmap-staging.yaml`: Set `REACT_APP_IL_API` to your staging API URL
- `configmap-production.yaml`: Set `REACT_APP_IL_API` to your production API URL

### 4. Update Ingress Domains
Edit the ingress files to set your domain names:
- `ingress-staging.yaml`: Set your staging domain
- `ingress-production.yaml`: Set your production domain

### 5. Deploy to Staging
```bash
./deploy.sh -e staging -t staging
```

### 6. Deploy to Production
```bash
./deploy.sh -e production -t latest
```

## Deployment Script Usage

The `deploy.sh` script provides a convenient way to deploy with zero-downtime:

```bash
# Deploy to staging
./deploy.sh -e staging -t v1.2.3

# Deploy to production with custom timeout
./deploy.sh -e production -t v2.0.0 -w 600

# Dry run (test without applying)
./deploy.sh -e staging -t v1.2.3 --dry-run
```

### Script Options
- `-e, --environment`: Environment (staging|production)
- `-t, --tag`: Docker image tag (required)
- `-n, --namespace`: Custom namespace
- `-d, --dry-run`: Test without applying changes
- `-w, --wait`: Rollout timeout in seconds
- `-h, --help`: Show help

## Rollback

If a deployment fails, use the rollback script:

```bash
# Rollback to previous version
./rollback.sh -e staging

# Rollback to specific revision
./rollback.sh -e production -r 3
```

## Zero-Downtime Deployment Features

### Rolling Update Strategy
- `maxUnavailable: 0`: Ensures no pods are terminated before new ones are ready
- `maxSurge: 1`: Allows one extra pod during updates

### Health Checks
- **Liveness Probe**: Restarts unhealthy containers
- **Readiness Probe**: Removes unready pods from service
- **Graceful Shutdown**: 30-second termination grace period

### Auto-Scaling
- **Staging**: 2-5 pods based on 70% CPU, 80% memory
- **Production**: 3-10 pods based on 70% CPU, 80% memory

## Monitoring and Troubleshooting

### Check Deployment Status
```bash
kubectl get deployments -n ryvyl-staging
kubectl get pods -n ryvyl-staging
kubectl get hpa -n ryvyl-staging
```

### View Logs
```bash
kubectl logs -f deployment/ryvyl-dashboard -n ryvyl-staging
```

### Check Rollout History
```bash
kubectl rollout history deployment/ryvyl-dashboard -n ryvyl-staging
```

### Debug Pod Issues
```bash
kubectl describe pod <pod-name> -n ryvyl-staging
```

## Configuration Management

### Environment Variables
Environment-specific configuration is managed through ConfigMaps:
- API endpoints
- Feature flags
- Environment identifiers

### Secrets
Sensitive data should be stored in Kubernetes Secrets:
- Database credentials
- API keys
- SSL certificates

## Security Considerations

1. **Non-root containers**: Production Dockerfile runs as nginx user
2. **Resource limits**: Prevents resource exhaustion
3. **Network policies**: Consider implementing for additional security
4. **RBAC**: Use role-based access control for cluster access
5. **Image scanning**: Scan images for vulnerabilities before deployment

## Performance Optimization

1. **Resource requests/limits**: Properly sized for each environment
2. **HPA configuration**: Responsive scaling based on metrics
3. **Nginx optimization**: Gzip compression and caching
4. **Multi-stage builds**: Smaller production images

## Maintenance

### Regular Tasks
1. **Update base images**: Keep Node.js and Nginx images updated
2. **Monitor resource usage**: Adjust limits based on actual usage
3. **Review scaling metrics**: Tune HPA thresholds
4. **Certificate renewal**: Ensure SSL certificates are renewed
5. **Backup configurations**: Version control all YAML files

### Disaster Recovery
1. **Database backups**: Regular backups of persistent data
2. **Configuration backups**: Store configs in version control
3. **Rollback procedures**: Test rollback scripts regularly
4. **Documentation**: Keep deployment procedures updated

## GitHub Actions Integration

Update your existing GitHub Actions workflows to use the new Kubernetes deployment:

### Staging Workflow (.github/workflows/staging.yaml)
```yaml
- name: Deploy to Kubernetes
  run: |
    echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
    export KUBECONFIG=kubeconfig
    cd k8s
    ./deploy.sh -e staging -t staging
```

### Production Workflow (.github/workflows/production.yaml)
```yaml
- name: Deploy to Kubernetes
  run: |
    echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
    export KUBECONFIG=kubeconfig
    cd k8s
    ./deploy.sh -e production -t ${{ github.event.inputs.image_tag || 'prod' }}
```

## Support

For issues or questions:
1. Check pod logs and events
2. Verify configuration values
3. Test connectivity to dependencies
4. Review resource usage and limits
5. Consult Kubernetes documentation
