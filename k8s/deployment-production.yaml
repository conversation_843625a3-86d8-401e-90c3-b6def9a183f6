apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-dashboard
  namespace: ryvyl-production
  labels:
    app: ryvyl-dashboard
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ryvyl-dashboard
      environment: production
  template:
    metadata:
      labels:
        app: ryvyl-dashboard
        environment: production
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ghcr.io/encorp-io/ryvyl-dashboard:prod
        ports:
        - containerPort: 4001
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: PORT
          value: "4001"
        - name: REACT_APP_IL_API
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: REACT_APP_IL_API
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 4001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 4001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
      terminationGracePeriodSeconds: 30
      imagePullSecrets:
      - name: ghcr-secret
