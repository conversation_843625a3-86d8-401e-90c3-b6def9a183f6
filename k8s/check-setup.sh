#!/bin/bash

# Setup Diagnostic Script for RYVYL Dashboard Kubernetes Testing
# This script checks if your environment is ready for local testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================================="
    echo "$1"
    echo "=================================================="
}

# Install Docker if not present
install_docker() {
    print_header "Installing Docker"

    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS installation
        if command -v brew &> /dev/null; then
            print_status "Installing Docker Desktop via Homebrew..."
            brew install --cask docker
            print_success "Docker Desktop installed via Homebrew"
            print_status "Please start Docker Desktop from Applications and enable Kubernetes"
            print_status "Then run this script again"
        else
            print_status "Homebrew not found. Installing Homebrew first..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            print_status "Installing Docker Desktop..."
            brew install --cask docker
            print_success "Docker Desktop installed"
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux installation
        print_status "Installing Docker on Linux..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        print_success "Docker installed. Please log out and back in, then run this script again"
    else
        print_error "Unsupported operating system: $OSTYPE"
        print_status "Please install Docker manually from: https://www.docker.com/products/docker-desktop"
        return 1
    fi
}

# Check Docker
check_docker() {
    print_header "Checking Docker Setup"

    # Check if Docker command exists
    if command -v docker &> /dev/null; then
        print_success "Docker command found: $(which docker)"
    elif [[ -f "/Applications/Docker.app/Contents/Resources/bin/docker" ]]; then
        print_warning "Docker found but not in PATH"
        print_status "Adding Docker to PATH..."
        export PATH="/Applications/Docker.app/Contents/Resources/bin:$PATH"
        if command -v docker &> /dev/null; then
            print_success "Docker command now available"
            print_status "Add this to your ~/.zshrc: export PATH=\"/Applications/Docker.app/Contents/Resources/bin:\$PATH\""
        fi
    else
        print_error "Docker not found"
        read -p "Would you like to install Docker automatically? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_docker
            return 1
        else
            print_status "Please install Docker manually from: https://www.docker.com/products/docker-desktop"
            return 1
        fi
    fi
    
    # Check Docker version
    if docker --version &> /dev/null; then
        print_success "Docker version: $(docker --version)"
    else
        print_error "Cannot get Docker version"
        return 1
    fi
    
    # Check if Docker daemon is running
    if docker info &> /dev/null; then
        print_success "Docker daemon is running"
        echo "  Docker info: $(docker info --format '{{.ServerVersion}}' 2>/dev/null || echo 'Unknown version')"
    else
        print_error "Docker daemon is not running"
        print_status "Please start Docker Desktop and try again"
        return 1
    fi
    
    # Check if we can build images
    if docker images &> /dev/null; then
        print_success "Can access Docker images"
        echo "  Available images: $(docker images --format 'table {{.Repository}}:{{.Tag}}' | wc -l) images"
    else
        print_error "Cannot access Docker images"
        return 1
    fi
}

# Check Kubernetes
check_kubernetes() {
    print_header "Checking Kubernetes Setup"
    
    # Check if kubectl exists
    if command -v kubectl &> /dev/null; then
        print_success "kubectl found: $(which kubectl)"
        print_status "kubectl version: $(kubectl version --client --short 2>/dev/null || echo 'Unknown')"
    else
        print_error "kubectl not found"
        print_status "Install kubectl: https://kubernetes.io/docs/tasks/tools/"
        return 1
    fi
    
    # Check cluster connectivity
    if kubectl cluster-info &> /dev/null; then
        print_success "Connected to Kubernetes cluster"
        echo "  Cluster: $(kubectl cluster-info | head -1 | sed 's/.*at //')"
    else
        print_error "Cannot connect to Kubernetes cluster"
        print_status "Enable Kubernetes in Docker Desktop:"
        print_status "  Docker Desktop → Settings → Kubernetes → Enable Kubernetes"
        return 1
    fi
    
    # Check nodes
    if kubectl get nodes &> /dev/null; then
        print_success "Can access cluster nodes"
        echo "  Nodes: $(kubectl get nodes --no-headers | wc -l) node(s)"
        kubectl get nodes --no-headers | while read line; do
            echo "    - $line"
        done
    else
        print_error "Cannot access cluster nodes"
        return 1
    fi
    
    # Check if we can create resources
    if kubectl auth can-i create pods &> /dev/null; then
        print_success "Have permissions to create resources"
    else
        print_warning "May not have sufficient permissions"
    fi
}

# Check system resources
check_resources() {
    print_header "Checking System Resources"
    
    # Check available memory
    if command -v free &> /dev/null; then
        MEMORY=$(free -h | awk '/^Mem:/ {print $2}')
        print_status "Available memory: $MEMORY"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        MEMORY=$(system_profiler SPHardwareDataType | grep "Memory:" | awk '{print $2 $3}')
        print_status "System memory: $MEMORY"
    fi
    
    # Check Docker Desktop resources (if on macOS)
    if [[ "$OSTYPE" == "darwin"* ]] && command -v docker &> /dev/null; then
        if docker info 2>/dev/null | grep -q "Total Memory"; then
            DOCKER_MEM=$(docker info 2>/dev/null | grep "Total Memory" | awk '{print $3 $4}')
            print_status "Docker Desktop memory: $DOCKER_MEM"
        fi
    fi
    
    # Check disk space
    if command -v df &> /dev/null; then
        DISK_USAGE=$(df -h . | tail -1 | awk '{print $4 " available"}')
        print_status "Disk space: $DISK_USAGE"
    fi
}

# Check project files
check_project() {
    print_header "Checking Project Files"
    
    # Check if we're in the right directory
    if [[ -f "./package.json" ]]; then
        print_success "Found package.json in parent directory"
    else
        print_error "package.json not found in parent directory"
        print_status "Make sure you're running this from the k8s/ directory"
        return 1
    fi
    
    # Check Dockerfile
    if [[ -f "./Dockerfile.production" ]]; then
        print_success "Found Dockerfile.production"
    else
        print_error "Dockerfile.production not found"
        return 1
    fi
    
    # Check test scripts
    if [[ -f "./k8s/test-simple.sh" ]]; then
        print_success "Found test-simple.sh"
    else
        print_warning "test-simple.sh not found"
    fi
    
    if [[ -f "./k8s/test-local.sh" ]]; then
        print_success "Found test-local.sh"
    else
        print_warning "test-local.sh not found"
    fi
}

# Provide recommendations
provide_recommendations() {
    print_header "Recommendations"
    
    echo "For local testing, you have these options:"
    echo ""
    echo "1. 🚀 Simple Testing (Recommended for beginners):"
    echo "   ./test-simple.sh deploy"
    echo "   kubectl port-forward svc/ryvyl-dashboard-service 8080:80"
    echo "   # Then open http://localhost:8080"
    echo ""
    echo "2. 🔧 Full Testing (With ingress):"
    echo "   ./test-local.sh setup"
    echo "   ./test-local.sh deploy"
    echo ""
    echo "3. 🐳 Manual Docker Testing:"
    echo "   docker build -f ../Dockerfile.production -t ryvyl-dashboard:test .."
    echo "   docker run -p 8080:80 ryvyl-dashboard:test"
    echo "   # Then open http://localhost:8080"
    echo ""
    echo "If you encounter issues:"
    echo "- Restart Docker Desktop"
    echo "- Check Docker Desktop has enough memory (8GB recommended)"
    echo "- Ensure Kubernetes is enabled in Docker Desktop settings"
}

# Main execution
main() {
    echo "🔍 RYVYL Dashboard - Local Testing Environment Check"
    echo "This script will verify your setup is ready for Kubernetes testing"
    
    OVERALL_STATUS=0
    
    if ! check_docker; then
        OVERALL_STATUS=1
    fi
    
    if ! check_kubernetes; then
        OVERALL_STATUS=1
    fi
    
    check_resources
    
    if ! check_project; then
        OVERALL_STATUS=1
    fi
    
    provide_recommendations
    
    print_header "Summary"
    
    if [[ $OVERALL_STATUS -eq 0 ]]; then
        print_success "✅ Your environment is ready for local Kubernetes testing!"
        echo ""
        echo "Next steps:"
        echo "1. Run: ./test-simple.sh deploy"
        echo "2. Run: kubectl port-forward svc/ryvyl-dashboard-service 8080:80"
        echo "3. Open: http://localhost:8080"
    else
        print_error "❌ Some issues need to be resolved before testing"
        echo ""
        echo "Please fix the issues above and run this script again"
    fi
}

main "$@"
