apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ryvyl-dashboard-hpa
  namespace: ryvyl-staging
  labels:
    app: ryvyl-dashboard
    environment: staging
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ryvyl-dashboard
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
