#!/bin/bash

# Kubernetes Cluster Setup Script for RYVYL Dashboard
# This script prepares the cluster for deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Setup Kubernetes cluster for RYVYL Dashboard deployment

OPTIONS:
    --docker-username   Docker registry username [required]
    --docker-password   Docker registry password/token [required]
    --staging-api       Staging API URL [required]
    --production-api    Production API URL [required]
    --staging-domain    Staging domain name [required]
    --production-domain Production domain name [required]
    --skip-ingress      Skip NGINX ingress controller installation
    --skip-cert-manager Skip cert-manager installation
    --skip-metrics      Skip metrics server installation
    -h, --help          Show this help message

EXAMPLES:
    $0 --docker-username myuser --docker-password mytoken \\
       --staging-api https://staging-api.example.com \\
       --production-api https://api.example.com \\
       --staging-domain staging.dashboard.example.com \\
       --production-domain dashboard.example.com

EOF
}

# Default values
DOCKER_USERNAME=""
DOCKER_PASSWORD=""
STAGING_API=""
PRODUCTION_API=""
STAGING_DOMAIN=""
PRODUCTION_DOMAIN=""
SKIP_INGRESS=false
SKIP_CERT_MANAGER=false
SKIP_METRICS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --docker-username)
            DOCKER_USERNAME="$2"
            shift 2
            ;;
        --docker-password)
            DOCKER_PASSWORD="$2"
            shift 2
            ;;
        --staging-api)
            STAGING_API="$2"
            shift 2
            ;;
        --production-api)
            PRODUCTION_API="$2"
            shift 2
            ;;
        --staging-domain)
            STAGING_DOMAIN="$2"
            shift 2
            ;;
        --production-domain)
            PRODUCTION_DOMAIN="$2"
            shift 2
            ;;
        --skip-ingress)
            SKIP_INGRESS=true
            shift
            ;;
        --skip-cert-manager)
            SKIP_CERT_MANAGER=true
            shift
            ;;
        --skip-metrics)
            SKIP_METRICS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$DOCKER_USERNAME" || -z "$DOCKER_PASSWORD" || -z "$STAGING_API" || -z "$PRODUCTION_API" || -z "$STAGING_DOMAIN" || -z "$PRODUCTION_DOMAIN" ]]; then
    print_error "All required parameters must be provided"
    show_usage
    exit 1
fi

print_status "Starting Kubernetes cluster setup for RYVYL Dashboard"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_warning "helm is not installed. Some components may need manual installation"
fi

# Check cluster connectivity
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_success "Connected to Kubernetes cluster"

# Create namespaces
print_status "Creating namespaces"
kubectl apply -f namespaces.yaml

# Install NGINX Ingress Controller
if [[ "$SKIP_INGRESS" == "false" ]]; then
    print_status "Installing NGINX Ingress Controller"
    if command -v helm &> /dev/null; then
        helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
        helm repo update
        helm install ingress-nginx ingress-nginx/ingress-nginx \
            --create-namespace \
            --namespace ingress-nginx \
            --set controller.service.type=LoadBalancer
    else
        kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.2/deploy/static/provider/cloud/deploy.yaml
    fi
    print_success "NGINX Ingress Controller installed"
else
    print_warning "Skipping NGINX Ingress Controller installation"
fi

# Install cert-manager
if [[ "$SKIP_CERT_MANAGER" == "false" ]]; then
    print_status "Installing cert-manager"
    if command -v helm &> /dev/null; then
        helm repo add jetstack https://charts.jetstack.io
        helm repo update
        helm install cert-manager jetstack/cert-manager \
            --namespace cert-manager \
            --create-namespace \
            --version v1.13.0 \
            --set installCRDs=true
    else
        kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
    fi
    print_success "cert-manager installed"
else
    print_warning "Skipping cert-manager installation"
fi

# Install metrics server
if [[ "$SKIP_METRICS" == "false" ]]; then
    print_status "Installing metrics server"
    kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    print_success "Metrics server installed"
else
    print_warning "Skipping metrics server installation"
fi

# Create Docker registry secrets
print_status "Creating Docker registry secrets"
kubectl create secret docker-registry ghcr-secret \
    --docker-server=ghcr.io \
    --docker-username="$DOCKER_USERNAME" \
    --docker-password="$DOCKER_PASSWORD" \
    --namespace=ryvyl-staging \
    --dry-run=client -o yaml | kubectl apply -f -

kubectl create secret docker-registry ghcr-secret \
    --docker-server=ghcr.io \
    --docker-username="$DOCKER_USERNAME" \
    --docker-password="$DOCKER_PASSWORD" \
    --namespace=ryvyl-production \
    --dry-run=client -o yaml | kubectl apply -f -

print_success "Docker registry secrets created"

# Update ConfigMaps with provided API URLs
print_status "Updating ConfigMaps with API URLs"
sed -i.bak "s|https://staging-api.yourdomain.com|$STAGING_API|g" configmap-staging.yaml
sed -i.bak "s|https://api.yourdomain.com|$PRODUCTION_API|g" configmap-production.yaml

# Update Ingress with provided domains
print_status "Updating Ingress with domain names"
sed -i.bak "s|staging.ryvyl-dashboard.yourdomain.com|$STAGING_DOMAIN|g" ingress-staging.yaml
sed -i.bak "s|ryvyl-dashboard.yourdomain.com|$PRODUCTION_DOMAIN|g" ingress-production.yaml

# Apply ConfigMaps
print_status "Applying ConfigMaps"
kubectl apply -f configmap-staging.yaml
kubectl apply -f configmap-production.yaml

print_success "Cluster setup completed successfully!"

print_status "Next steps:"
echo "1. Verify all components are running:"
echo "   kubectl get pods -A"
echo ""
echo "2. Deploy to staging:"
echo "   ./deploy.sh -e staging -t staging"
echo ""
echo "3. Deploy to production:"
echo "   ./deploy.sh -e production -t latest"
echo ""
echo "4. Check your applications:"
echo "   Staging: https://$STAGING_DOMAIN"
echo "   Production: https://$PRODUCTION_DOMAIN"
