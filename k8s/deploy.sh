#!/bin/bash

# Kubernetes Deployment Script for RYVYL Dashboard
# This script provides zero-downtime deployment capabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="staging"
NAMESPACE=""
IMAGE_TAG=""
DRY_RUN=false
WAIT_TIMEOUT=300

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy RYVYL Dashboard to Kubernetes with zero-downtime

OPTIONS:
    -e, --environment    Environment (staging|production) [default: staging]
    -t, --tag           Docker image tag [required]
    -n, --namespace     Kubernetes namespace [auto-detected from environment]
    -d, --dry-run       Perform a dry run without applying changes
    -w, --wait          Wait timeout in seconds [default: 300]
    -h, --help          Show this help message

EXAMPLES:
    $0 -e staging -t v1.2.3
    $0 -e production -t latest --dry-run
    $0 --environment production --tag v2.0.0 --wait 600

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -w|--wait)
            WAIT_TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$IMAGE_TAG" ]]; then
    print_error "Image tag is required. Use -t or --tag option."
    show_usage
    exit 1
fi

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Environment must be 'staging' or 'production'"
    exit 1
fi

# Set namespace if not provided
if [[ -z "$NAMESPACE" ]]; then
    NAMESPACE="ryvyl-${ENVIRONMENT}"
fi

print_status "Starting deployment to $ENVIRONMENT environment"
print_status "Namespace: $NAMESPACE"
print_status "Image tag: $IMAGE_TAG"
print_status "Dry run: $DRY_RUN"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

# Function to apply Kubernetes manifests
apply_manifest() {
    local file=$1
    local action="apply"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        action="apply --dry-run=client"
    fi
    
    if [[ -f "$file" ]]; then
        print_status "Applying $file"
        kubectl $action -f "$file"
    else
        print_warning "File $file not found, skipping"
    fi
}

# Create namespace if it doesn't exist
print_status "Ensuring namespace exists"
if [[ "$DRY_RUN" == "false" ]]; then
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
fi

# Apply configurations in order
print_status "Applying ConfigMaps"
apply_manifest "configmap-${ENVIRONMENT}.yaml"

print_status "Applying Services"
apply_manifest "service-${ENVIRONMENT}.yaml"

# Update deployment with new image
print_status "Updating deployment with new image"
if [[ "$DRY_RUN" == "false" ]]; then
    # Update the deployment image
    kubectl set image deployment/ryvyl-dashboard \
        ryvyl-dashboard=ghcr.io/encorp-io/ryvyl-dashboard:${IMAGE_TAG} \
        -n "$NAMESPACE"
    
    # Wait for rollout to complete
    print_status "Waiting for deployment rollout to complete (timeout: ${WAIT_TIMEOUT}s)"
    kubectl rollout status deployment/ryvyl-dashboard -n "$NAMESPACE" --timeout="${WAIT_TIMEOUT}s"
    
    if [[ $? -eq 0 ]]; then
        print_success "Deployment rollout completed successfully"
    else
        print_error "Deployment rollout failed or timed out"
        print_status "Rolling back to previous version"
        kubectl rollout undo deployment/ryvyl-dashboard -n "$NAMESPACE"
        exit 1
    fi
else
    print_status "Dry run: Would update deployment image to ghcr.io/encorp-io/ryvyl-dashboard:${IMAGE_TAG}"
fi

print_status "Applying Ingress"
apply_manifest "ingress-${ENVIRONMENT}.yaml"

print_status "Applying HorizontalPodAutoscaler"
apply_manifest "hpa-${ENVIRONMENT}.yaml"

# Show deployment status
if [[ "$DRY_RUN" == "false" ]]; then
    print_status "Deployment status:"
    kubectl get pods -n "$NAMESPACE" -l app=ryvyl-dashboard
    
    print_status "Service status:"
    kubectl get svc -n "$NAMESPACE" -l app=ryvyl-dashboard
    
    print_status "Ingress status:"
    kubectl get ingress -n "$NAMESPACE" -l app=ryvyl-dashboard
fi

print_success "Deployment completed successfully!"

if [[ "$ENVIRONMENT" == "staging" ]]; then
    print_status "Staging URL: https://staging.ryvyl-dashboard.yourdomain.com"
else
    print_status "Production URL: https://ryvyl-dashboard.yourdomain.com"
fi
