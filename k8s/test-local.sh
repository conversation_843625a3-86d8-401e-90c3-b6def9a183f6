#!/bin/bash

# Local Kubernetes Testing Script for RYVYL Dashboard
# This script sets up a local testing environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [COMMAND] [OPTIONS]

Local testing commands for RYVYL Dashboard Kubernetes deployment

COMMANDS:
    setup       Setup local Kubernetes environment
    deploy      Deploy application locally
    test        Run deployment tests
    cleanup     Clean up local resources
    status      Show current status

OPTIONS:
    --platform  Kubernetes platform (docker-desktop|minikube|kind) [default: docker-desktop]
    --image-tag Local image tag [default: local]
    -h, --help  Show this help message

EXAMPLES:
    $0 setup --platform docker-desktop
    $0 deploy --image-tag v1.0.0
    $0 test
    $0 cleanup

EOF
}

# Default values
COMMAND=""
PLATFORM="docker-desktop"
IMAGE_TAG="local"
LOCAL_DOMAIN="local.ryvyl-dashboard.com"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        setup|deploy|test|cleanup|status)
            COMMAND="$1"
            shift
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            if [[ -z "$COMMAND" ]]; then
                COMMAND="$1"
                shift
            else
                print_error "Unknown option: $1"
                show_usage
                exit 1
            fi
            ;;
    esac
done

if [[ -z "$COMMAND" ]]; then
    print_error "Command is required"
    show_usage
    exit 1
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Function to setup local environment
setup_local() {
    print_status "Setting up local Kubernetes environment with $PLATFORM"
    
    case $PLATFORM in
        docker-desktop)
            print_status "Checking Docker Desktop Kubernetes..."
            if ! kubectl cluster-info &> /dev/null; then
                print_error "Docker Desktop Kubernetes is not running. Please enable it in Docker Desktop settings."
                exit 1
            fi
            
            print_status "Installing NGINX Ingress Controller..."
            # Try the latest version first, fallback to specific version
            if ! kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/main/deploy/static/provider/docker-desktop/deploy.yaml; then
                print_warning "Latest version failed, trying alternative installation method..."
                kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.10.0/deploy/static/provider/cloud/deploy.yaml
            fi
            ;;
            
        minikube)
            print_status "Starting Minikube..."
            if ! command -v minikube &> /dev/null; then
                print_error "Minikube is not installed"
                exit 1
            fi
            
            minikube start --memory=8192 --cpus=4
            minikube addons enable ingress
            minikube addons enable metrics-server
            ;;
            
        kind)
            print_status "Setting up Kind cluster..."
            if ! command -v kind &> /dev/null; then
                print_error "Kind is not installed"
                exit 1
            fi
            
            # Create kind config if it doesn't exist
            if [[ ! -f "kind-config.yaml" ]]; then
                cat > kind-config.yaml << EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 80
    hostPort: 80
    protocol: TCP
  - containerPort: 443
    hostPort: 443
    protocol: TCP
EOF
            fi
            
            kind create cluster --config=kind-config.yaml
            print_status "Installing NGINX Ingress for Kind..."
            kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/main/deploy/static/provider/kind/deploy.yaml
            ;;
            
        *)
            print_error "Unsupported platform: $PLATFORM"
            exit 1
            ;;
    esac
    
    print_status "Waiting for ingress controller to be ready..."
    kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s
    
    print_success "Local Kubernetes environment is ready!"
}

# Function to build and deploy locally
deploy_local() {
    print_status "Building and deploying application locally"
    export PATH="/Applications/Docker.app/Contents/Resources/bin:$PATH"

    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        print_status "Please ensure Docker Desktop is installed and running"
        print_status "You can download it from: https://www.docker.com/products/docker-desktop"
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        print_status "Please start Docker Desktop and try again"
        exit 1
    fi

    # Build the image
    print_status "Building Docker image..."

    # Verify Dockerfile exists
    if [[ ! -f "Dockerfile.dev" ]]; then
        print_error "Dockerfile.dev not found in project root"
        print_status "Current directory: $(pwd)"
        print_status "Files: $(ls -la | grep Dockerfile || echo 'No Dockerfile found')"
        exit 1
    fi

    # Build the Docker image
    print_status "Building from $(pwd) with Dockerfile.dev"
    if ! docker build -f Dockerfile.dev -t ryvyl-dashboard:$IMAGE_TAG .; then
        print_error "Failed to build Docker image"
        exit 1
    fi
    
    # Load image into cluster
    case $PLATFORM in
        minikube)
            print_status "Loading image into Minikube..."
            minikube image load ryvyl-dashboard:$IMAGE_TAG
            ;;
        kind)
            print_status "Loading image into Kind..."
            kind load docker-image ryvyl-dashboard:$IMAGE_TAG
            ;;
        docker-desktop)
            print_status "Image available in Docker Desktop..."
            ;;
    esac
    
    # Create local deployment configuration
    print_status "Creating local deployment configuration..."
    
    cat > deployment-local.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-dashboard
  namespace: default
  labels:
    app: ryvyl-dashboard
    environment: local
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ryvyl-dashboard
      environment: local
  template:
    metadata:
      labels:
        app: ryvyl-dashboard
        environment: local
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ryvyl-dashboard:$IMAGE_TAG
        imagePullPolicy: Never
        ports:
        - containerPort: 3001
          name: http
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
EOF

    cat > service-local.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-dashboard-service
  namespace: default
  labels:
    app: ryvyl-dashboard
    environment: local
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: ryvyl-dashboard
    environment: local
EOF

    cat > ingress-local.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-dashboard-ingress
  namespace: default
  labels:
    app: ryvyl-dashboard
    environment: local
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: $LOCAL_DOMAIN
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-dashboard-service
            port:
              number: 80
EOF
    
    # Deploy to cluster
    print_status "Deploying to local cluster..."
    kubectl apply -f deployment-local.yaml
    kubectl apply -f service-local.yaml
    kubectl apply -f ingress-local.yaml
    
    # Wait for deployment
    print_status "Waiting for deployment to be ready..."
    kubectl rollout status deployment/ryvyl-dashboard --timeout=300s
    
    # Update hosts file
    print_status "Updating hosts file..."
    if [[ "$PLATFORM" == "minikube" ]]; then
        CLUSTER_IP=$(minikube ip)
    else
        CLUSTER_IP="127.0.0.1"
    fi
    
    if ! grep -q "$LOCAL_DOMAIN" /etc/hosts; then
        echo "$CLUSTER_IP $LOCAL_DOMAIN" | sudo tee -a /etc/hosts
        print_success "Added $LOCAL_DOMAIN to hosts file"
    else
        print_warning "$LOCAL_DOMAIN already exists in hosts file"
    fi
    
    print_success "Application deployed successfully!"
    print_status "Access your application at: http://$LOCAL_DOMAIN"
}

# Function to test the deployment
test_deployment() {
    print_status "Testing local deployment..."
    
    # Check if pods are running
    print_status "Checking pod status..."
    kubectl get pods -l app=ryvyl-dashboard
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    if curl -f http://$LOCAL_DOMAIN/health &> /dev/null; then
        print_success "Health check passed"
    else
        print_error "Health check failed"
    fi
    
    # Test main application
    print_status "Testing main application..."
    if curl -f http://$LOCAL_DOMAIN &> /dev/null; then
        print_success "Application is responding"
    else
        print_error "Application is not responding"
    fi
    
    # Test rolling update
    print_status "Testing rolling update..."
    kubectl set image deployment/ryvyl-dashboard ryvyl-dashboard=ryvyl-dashboard:$IMAGE_TAG
    kubectl rollout status deployment/ryvyl-dashboard --timeout=300s
    
    print_success "All tests passed!"
}

# Function to show status
show_status() {
    print_status "Current deployment status:"
    
    echo ""
    echo "Pods:"
    kubectl get pods -l app=ryvyl-dashboard
    
    echo ""
    echo "Services:"
    kubectl get svc -l app=ryvyl-dashboard
    
    echo ""
    echo "Ingress:"
    kubectl get ingress -l app=ryvyl-dashboard
    
    echo ""
    echo "Application URL: http://$LOCAL_DOMAIN"
}

# Function to cleanup
cleanup_local() {
    print_status "Cleaning up local resources..."
    
    kubectl delete -f deployment-local.yaml --ignore-not-found=true
    kubectl delete -f service-local.yaml --ignore-not-found=true
    kubectl delete -f ingress-local.yaml --ignore-not-found=true
    
    # Remove from hosts file
    sudo sed -i.bak "/$LOCAL_DOMAIN/d" /etc/hosts
    
    # Clean up temporary files
    rm -f deployment-local.yaml service-local.yaml ingress-local.yaml kind-config.yaml
    
    print_success "Cleanup completed!"
}

# Execute command
case $COMMAND in
    setup)
        setup_local
        ;;
    deploy)
        deploy_local
        ;;
    test)
        test_deployment
        ;;
    status)
        show_status
        ;;
    cleanup)
        cleanup_local
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
