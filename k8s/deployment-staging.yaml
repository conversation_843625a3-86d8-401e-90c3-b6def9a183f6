apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-dashboard
  namespace: ryvyl-staging
  labels:
    app: ryvyl-dashboard
    environment: staging
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ryvyl-dashboard
      environment: staging
  template:
    metadata:
      labels:
        app: ryvyl-dashboard
        environment: staging
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ghcr.io/encorp-io/ryvyl-dashboard:staging
        ports:
        - containerPort: 3001
          name: http
        env:
        - name: ENVIRONMENT
          value: "staging"
        - name: PORT
          value: "3001"
        - name: REACT_APP_IL_API
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: REACT_APP_IL_API
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
      terminationGracePeriodSeconds: 30
      imagePullSecrets:
      - name: ghcr-secret
