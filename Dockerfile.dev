# Development/Build Dockerfile that avoids husky issues
FROM node:21-alpine

ARG REACT_APP_IL_API
ARG PORT=3000
ARG ENVIRONMENT=staging

ENV REACT_APP_IL_API=${REACT_APP_IL_API}
ENV ENVIRONMENT=${ENVIRONMENT}
ENV PORT=${PORT}
ENV HUSKY=0
ENV CI=true

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies without running scripts (avoids husky)
RUN npm install --ignore-scripts

# Copy source code
COPY . .

EXPOSE ${PORT}

# Start the development server (or build for production)
CMD ["sh", "-c", "if [ \"$ENVIRONMENT\" = \"production\" ]; then npm run build && npx serve -s build -l $PORT; else npm run start-$ENVIRONMENT; fi"]
