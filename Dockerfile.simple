# Simple production Dockerfile without husky issues
FROM node:21-alpine AS builder

ARG REACT_APP_IL_API
ARG ENVIRONMENT=production

ENV REACT_APP_IL_API=${REACT_APP_IL_API}
ENV ENVIRONMENT=${ENVIRONMENT}
ENV NODE_ENV=production
ENV GENERATE_SOURCEMAP=false
ENV HUSKY=0

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies without scripts (avoids husky)
RUN npm ci --ignore-scripts

# Copy source code (exclude node_modules and other unnecessary files)
COPY public/ ./public/
COPY src/ ./src/
COPY jsconfig.json ./

# Build the application
RUN npm run build

# Production stage with nginx
FROM nginx:1.25-alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# Switch to non-root user
USER nginx

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
