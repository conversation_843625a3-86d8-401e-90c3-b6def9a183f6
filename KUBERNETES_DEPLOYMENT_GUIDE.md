# Kubernetes Deployment Guide for RYVYL Dashboard

## Overview

This guide provides step-by-step instructions for deploying the RYVYL Dashboard to Kubernetes with zero-downtime capabilities. The setup ensures that during deployments, the old version continues to serve traffic until the new version is fully ready and healthy.

## Zero-Downtime Deployment Strategy

### How It Works

1. **Rolling Updates**: Kubernetes gradually replaces old pods with new ones
2. **Health Checks**: New pods must pass health checks before receiving traffic
3. **Traffic Switching**: Load balancer only routes to healthy pods
4. **Graceful Shutdown**: Old pods are given time to finish existing requests

### Key Configuration

- `maxUnavailable: 0`: No pods are terminated until new ones are ready
- `maxSurge: 1`: One additional pod is created during updates
- **Readiness Probes**: Ensure pods are ready before receiving traffic
- **Liveness Probes**: Restart unhealthy pods automatically

## Prerequisites

### 1. Kubernetes Cluster
- Kubernetes v1.19 or later
- At least 2 worker nodes (recommended for high availability)
- Sufficient resources: 4 CPU cores, 8GB RAM minimum

### 2. Required Tools
```bash
# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# Install helm (optional but recommended)
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

### 3. Cluster Components
- **NGINX Ingress Controller**: For external access
- **cert-manager**: For SSL certificate management
- **Metrics Server**: For horizontal pod autoscaling

## Step-by-Step Deployment

### Step 1: Prepare Your Environment

1. **Clone the repository**:
```bash
git clone https://github.com/encorp-io/ryvyl-dashboard.git
cd ryvyl-dashboard/k8s
```

2. **Configure kubectl** to connect to your cluster:
```bash
# For cloud providers, follow their specific instructions
# For example, with Google Cloud:
gcloud container clusters get-credentials your-cluster-name --zone your-zone
```

### Step 2: Initial Cluster Setup

Run the setup script to prepare your cluster:

```bash
./setup.sh \
  --docker-username YOUR_GITHUB_USERNAME \
  --docker-password YOUR_GITHUB_TOKEN \
  --staging-api https://staging-api.yourdomain.com \
  --production-api https://api.yourdomain.com \
  --staging-domain staging.dashboard.yourdomain.com \
  --production-domain dashboard.yourdomain.com
```

This script will:
- Install required cluster components
- Create namespaces
- Set up Docker registry secrets
- Configure environment-specific settings

### Step 3: Verify Cluster Setup

Check that all components are running:

```bash
# Check all pods across namespaces
kubectl get pods -A

# Verify ingress controller
kubectl get pods -n ingress-nginx

# Check cert-manager
kubectl get pods -n cert-manager

# Verify metrics server
kubectl get pods -n kube-system | grep metrics-server
```

### Step 4: Deploy to Staging

Deploy your application to the staging environment:

```bash
./deploy.sh -e staging -t staging
```

Monitor the deployment:

```bash
# Watch deployment progress
kubectl get pods -n ryvyl-staging -w

# Check deployment status
kubectl rollout status deployment/ryvyl-dashboard -n ryvyl-staging
```

### Step 5: Verify Staging Deployment

1. **Check pod status**:
```bash
kubectl get pods -n ryvyl-staging -l app=ryvyl-dashboard
```

2. **Test application health**:
```bash
kubectl port-forward -n ryvyl-staging svc/ryvyl-dashboard-service 8080:80
# Open http://localhost:8080 in your browser
```

3. **Check ingress**:
```bash
kubectl get ingress -n ryvyl-staging
```

### Step 6: Deploy to Production

Once staging is verified, deploy to production:

```bash
./deploy.sh -e production -t latest
```

## Deployment Verification

### Health Checks

The deployment includes comprehensive health checks:

1. **Readiness Probe**: Checks if the application is ready to serve traffic
2. **Liveness Probe**: Monitors application health and restarts if needed

### Monitoring Commands

```bash
# Check deployment status
kubectl get deployments -n ryvyl-production

# View pod details
kubectl describe pods -n ryvyl-production -l app=ryvyl-dashboard

# Check application logs
kubectl logs -f deployment/ryvyl-dashboard -n ryvyl-production

# Monitor resource usage
kubectl top pods -n ryvyl-production
```

## Troubleshooting

### Common Issues

1. **Pods not starting**:
```bash
kubectl describe pod <pod-name> -n <namespace>
kubectl logs <pod-name> -n <namespace>
```

2. **Image pull errors**:
```bash
# Check if secret exists
kubectl get secrets -n <namespace>

# Verify secret content
kubectl get secret ghcr-secret -n <namespace> -o yaml
```

3. **Health check failures**:
```bash
# Check probe configuration
kubectl describe deployment ryvyl-dashboard -n <namespace>

# Test health endpoint manually
kubectl exec -it <pod-name> -n <namespace> -- curl localhost:80/health
```

### Rollback Procedure

If a deployment fails, quickly rollback:

```bash
# Rollback to previous version
./rollback.sh -e production

# Or rollback to specific revision
./rollback.sh -e production -r 3
```

## Scaling and Performance

### Horizontal Pod Autoscaler

The setup includes automatic scaling based on:
- CPU usage (target: 70%)
- Memory usage (target: 80%)

**Staging**: 2-5 pods
**Production**: 3-10 pods

### Manual Scaling

```bash
# Scale manually if needed
kubectl scale deployment ryvyl-dashboard --replicas=5 -n ryvyl-production
```

### Resource Optimization

Monitor and adjust resource requests/limits:

```bash
# Check current resource usage
kubectl top pods -n ryvyl-production

# Update deployment with new limits
kubectl patch deployment ryvyl-dashboard -n ryvyl-production -p '{"spec":{"template":{"spec":{"containers":[{"name":"ryvyl-dashboard","resources":{"limits":{"memory":"1Gi","cpu":"1000m"}}}]}}}}'
```

## Security Best Practices

1. **Use non-root containers**: Production Dockerfile runs as nginx user
2. **Resource limits**: Prevent resource exhaustion attacks
3. **Network policies**: Implement if additional network security is needed
4. **RBAC**: Use role-based access control for cluster access
5. **Image scanning**: Scan images for vulnerabilities before deployment

## Maintenance

### Regular Tasks

1. **Update dependencies**: Keep base images and dependencies updated
2. **Monitor metrics**: Review CPU, memory, and network usage
3. **Certificate renewal**: Ensure SSL certificates are automatically renewed
4. **Backup configurations**: Keep all YAML files in version control

### Disaster Recovery

1. **Configuration backup**: All configurations are in Git
2. **Database backup**: Implement regular backups for any persistent data
3. **Multi-region setup**: Consider deploying across multiple regions
4. **Monitoring alerts**: Set up alerts for critical failures

## Integration with CI/CD

### GitHub Actions Integration

Update your existing workflows to deploy to Kubernetes:

```yaml
# Add to .github/workflows/staging.yaml
- name: Deploy to Kubernetes
  run: |
    echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
    export KUBECONFIG=kubeconfig
    cd k8s
    ./deploy.sh -e staging -t staging

# Add to .github/workflows/production.yaml
- name: Deploy to Kubernetes
  run: |
    echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
    export KUBECONFIG=kubeconfig
    cd k8s
    ./deploy.sh -e production -t ${{ github.event.inputs.image_tag || 'prod' }}
```

### Required Secrets

Add these secrets to your GitHub repository:
- `KUBECONFIG`: Base64-encoded kubeconfig file
- `DOCKER_SECRET`: GitHub token for container registry access

## Support and Documentation

For additional help:
- Review the [k8s/README.md](k8s/README.md) for detailed configuration options
- Check Kubernetes documentation for troubleshooting
- Monitor application logs and metrics
- Use the provided scripts for common operations

## Next Steps

1. Set up monitoring and alerting (Prometheus/Grafana)
2. Implement backup strategies
3. Configure log aggregation (ELK stack)
4. Set up development/testing environments
5. Implement advanced security policies
