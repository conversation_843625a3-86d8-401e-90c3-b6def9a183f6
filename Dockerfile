# Multi-stage build for optimized production images
# Stage 1: Build stage
FROM node:21-alpine AS builder

ARG REACT_APP_IL_API
ARG ENVIRONMENT=staging

ENV REACT_APP_IL_API=${REACT_APP_IL_API}
ENV ENVIRONMENT=${ENVIRONMENT}
ENV NODE_ENV=production

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production stage
FROM nginx:alpine AS production

# Install Node.js for serving React app
RUN apk add --no-cache nodejs npm

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# Stage 3: Development stage (for local development)
FROM node:21-alpine AS development

ARG REACT_APP_IL_API
ARG PORT=3000
ARG ENVIRONMENT=staging

ENV REACT_APP_IL_API=${REACT_APP_IL_API}
ENV ENVIRONMENT=${ENVIRONMENT}
ENV PORT=${PORT}

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

EXPOSE ${PORT}

# Start development server
CMD ["sh", "-c", "npm run start-$ENVIRONMENT"]