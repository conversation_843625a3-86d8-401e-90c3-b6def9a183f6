# Local Testing Guide for Kubernetes Deployment

## Overview

This guide shows you how to test the Kubernetes deployment locally before deploying to production. We'll cover multiple approaches from simple to comprehensive.

## Option 1: Docker Desktop with Kubernetes (Recommended for Beginners)

### Prerequisites
- Docker Desktop installed
- At least 8GB RAM allocated to Docker

### Setup Steps

1. **Enable Kubernetes in Docker Desktop**:
   - Open Docker Desktop settings
   - Go to "Kubernetes" tab
   - Check "Enable Kubernetes"
   - Click "Apply & Restart"

2. **Verify Kubernetes is running**:
```bash
kubectl cluster-info
kubectl get nodes
```

3. **Install NGINX Ingress Controller**:
```bash
# For Docker Desktop
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/main/deploy/static/provider/cloud/deploy.yaml

# Alternative method using Helm (recommended)
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm install ingress-nginx ingress-nginx/ingress-nginx --create-namespace --namespace ingress-nginx
```

4. **Wait for ingress controller to be ready**:
```bash
kubectl wait --namespace ingress-nginx \
  --for=condition=ready pod \
  --selector=app.kubernetes.io/component=controller \
  --timeout=90s
```

### Testing the Deployment

1. **Build your image locally**:
```bash
# Build the production image
docker build -f Dockerfile.production -t ryvyl-dashboard:local .

# Tag it for local registry
docker tag ryvyl-dashboard:local ghcr.io/encorp-io/ryvyl-dashboard:local
```

2. **Create a local test configuration**:
```bash
cd k8s
cp deployment-staging.yaml deployment-local.yaml
```

3. **Edit the local deployment file**:
```yaml
# In deployment-local.yaml, change:
metadata:
  name: ryvyl-dashboard
  namespace: default  # Use default namespace for local testing
spec:
  template:
    spec:
      containers:
      - name: ryvyl-dashboard
        image: ryvyl-dashboard:local  # Use local image
        imagePullPolicy: Never  # Don't try to pull from registry
      imagePullSecrets: []  # Remove registry secrets
```

4. **Create local service and ingress**:
```bash
# Create local service
cat > service-local.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-dashboard-service
  namespace: default
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: ryvyl-dashboard
EOF

# Create local ingress
cat > ingress-local.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-dashboard-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: local.ryvyl-dashboard.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-dashboard-service
            port:
              number: 80
EOF
```

5. **Deploy locally**:
```bash
kubectl apply -f deployment-local.yaml
kubectl apply -f service-local.yaml
kubectl apply -f ingress-local.yaml
```

6. **Add local domain to hosts file**:
```bash
# Add this line to /etc/hosts (Linux/Mac) or C:\Windows\System32\drivers\etc\hosts (Windows)
127.0.0.1 local.ryvyl-dashboard.com
```

7. **Test the application**:
```bash
# Check if pods are running
kubectl get pods

# Test the application
curl http://local.ryvyl-dashboard.com
# Or open http://local.ryvyl-dashboard.com in your browser
```

## Option 2: Minikube (More Production-Like)

### Setup Minikube

1. **Install Minikube**:
```bash
# On macOS
brew install minikube

# On Linux
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube
```

2. **Start Minikube**:
```bash
minikube start --memory=8192 --cpus=4
```

3. **Enable ingress addon**:
```bash
minikube addons enable ingress
minikube addons enable metrics-server
```

4. **Build and load image**:
```bash
# Build image
docker build -f Dockerfile.production -t ryvyl-dashboard:local .

# Load image into minikube
minikube image load ryvyl-dashboard:local
```

### Testing with Minikube

1. **Use the same local deployment files** from Option 1

2. **Deploy to minikube**:
```bash
kubectl apply -f deployment-local.yaml
kubectl apply -f service-local.yaml
kubectl apply -f ingress-local.yaml
```

3. **Get minikube IP**:
```bash
minikube ip
```

4. **Add to hosts file**:
```bash
# Replace <MINIKUBE_IP> with the actual IP
echo "<MINIKUBE_IP> local.ryvyl-dashboard.com" | sudo tee -a /etc/hosts
```

5. **Test the application**:
```bash
curl http://local.ryvyl-dashboard.com
```

## Option 3: Kind (Kubernetes in Docker)

### Setup Kind

1. **Install Kind**:
```bash
# On macOS
brew install kind

# On Linux
curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
chmod +x ./kind
sudo mv ./kind /usr/local/bin/kind
```

2. **Create cluster with ingress support**:
```bash
cat > kind-config.yaml << EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 80
    hostPort: 80
    protocol: TCP
  - containerPort: 443
    hostPort: 443
    protocol: TCP
EOF

kind create cluster --config=kind-config.yaml
```

3. **Install NGINX Ingress**:
```bash
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/main/deploy/static/provider/kind/deploy.yaml
```

4. **Load image into Kind**:
```bash
docker build -f Dockerfile.production -t ryvyl-dashboard:local .
kind load docker-image ryvyl-dashboard:local
```

## Testing Zero-Downtime Deployments Locally

### Simulate a Rolling Update

1. **Deploy initial version**:
```bash
kubectl apply -f deployment-local.yaml
kubectl rollout status deployment/ryvyl-dashboard
```

2. **Generate some traffic** (in a separate terminal):
```bash
# Continuous requests to test zero downtime
while true; do
  curl -s http://local.ryvyl-dashboard.com > /dev/null && echo "✓ $(date)" || echo "✗ $(date)"
  sleep 1
done
```

3. **Update the deployment** (simulate new version):
```bash
# Change the image tag to trigger an update
kubectl set image deployment/ryvyl-dashboard ryvyl-dashboard=ryvyl-dashboard:local-v2

# Watch the rolling update
kubectl rollout status deployment/ryvyl-dashboard
kubectl get pods -w
```

4. **Observe zero downtime**: The traffic script should show no failed requests during the update.

### Test Health Checks

1. **Check readiness probe**:
```bash
kubectl describe pod <pod-name>
```

2. **Simulate unhealthy pod**:
```bash
# Exec into pod and break the health endpoint
kubectl exec -it <pod-name> -- rm /usr/share/nginx/html/index.html

# Watch Kubernetes restart the pod
kubectl get pods -w
```

### Test Auto-Scaling (if metrics-server is available)

1. **Apply HPA**:
```bash
kubectl apply -f hpa-staging.yaml
```

2. **Generate load**:
```bash
# Install stress testing tool
kubectl run -i --tty load-generator --rm --image=busybox --restart=Never -- /bin/sh

# Inside the pod, generate requests
while true; do wget -q -O- http://ryvyl-dashboard-service; done
```

3. **Watch scaling**:
```bash
kubectl get hpa -w
kubectl get pods -w
```

## Debugging and Troubleshooting

### Common Commands

```bash
# Check pod status
kubectl get pods -o wide

# View pod logs
kubectl logs <pod-name>

# Describe pod for events
kubectl describe pod <pod-name>

# Check service endpoints
kubectl get endpoints

# Test service connectivity
kubectl run test-pod --image=busybox --rm -it --restart=Never -- /bin/sh
# Inside pod: wget -qO- http://ryvyl-dashboard-service

# Port forward for direct access
kubectl port-forward svc/ryvyl-dashboard-service 8080:80
# Then access http://localhost:8080
```

### Cleanup

```bash
# Delete local resources
kubectl delete -f deployment-local.yaml
kubectl delete -f service-local.yaml
kubectl delete -f ingress-local.yaml

# For minikube
minikube delete

# For kind
kind delete cluster

# For Docker Desktop
# Just disable Kubernetes in settings
```

## Validation Checklist

Before deploying to production, verify:

- [ ] Pods start successfully
- [ ] Health checks pass
- [ ] Service routes traffic correctly
- [ ] Ingress provides external access
- [ ] Rolling updates work without downtime
- [ ] Auto-scaling responds to load
- [ ] Rollback works correctly
- [ ] Resource limits are respected
- [ ] Logs are accessible

## Next Steps

Once local testing is successful:

1. **Test on a staging cluster** that mirrors production
2. **Run load tests** to validate performance
3. **Test disaster recovery** scenarios
4. **Validate monitoring and alerting**
5. **Deploy to production** with confidence

## Tips for Effective Local Testing

1. **Use resource limits** similar to production
2. **Test with realistic data volumes**
3. **Simulate network issues** using chaos engineering tools
4. **Test backup and restore procedures**
5. **Validate security configurations**
